#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强的桥梁分析器模块
支持内存直接传递模式
"""

import numpy as np
import openseespy.opensees as ops
from typing import Dict, <PERSON><PERSON>
from analysis.analyzer import BridgeAnalyzer
from analysis.recorder.bearing import find_bearings_closest_to_y0


class EnhancedBridgeAnalyzer(BridgeAnalyzer):
    """增强的桥梁分析器 (内存直接传递)"""
    
    def __init__(self, model):
        super().__init__(model)
        self.response_collector = None
        self.response_data = {
            'pier_responses': {},
            'bearing_responses': {},
            'time_history': []
        }
        
    def set_response_collector(self, collector):
        """设置响应数据收集器"""
        self.response_collector = collector
        
    def _record_responses(self, current_time):
        """重写响应记录方法"""
        if self.response_collector:
            self._record_responses_to_memory(current_time)
        else:
            # 调用父类的文件记录方法
            super()._record_responses(current_time)
            
    def _record_responses_to_memory(self, current_time):
        """将响应数据直接记录到内存"""
        # 记录时间点
        self.response_data['time_history'].append(current_time)

        # 记录桥墩响应
        self._record_pier_responses_to_memory(current_time)

        # 记录支座相对位移（与原始分析器保持一致）
        self._record_bearing_relative_displacements_to_memory(current_time)

        # 记录支座力和变形数据（用于与文件模式兼容）
        self._record_bearing_forces_to_memory(current_time)
        
    def _record_pier_responses_to_memory(self, current_time):
        """记录桥墩响应到内存"""
        if not self.model.piers['nodes']:
            return

        # 记录所有桥墩（与文件模式保持一致）
        for pier_key, pier_nodes in self.model.piers['nodes'].items():
            # 获取桥墩顶部节点和底部节点
            top_node = pier_nodes[-1]
            bottom_node = pier_nodes[0]

            # 获取顶部节点位移
            top_disp_x = ops.nodeDisp(top_node, 1)
            top_disp_y = ops.nodeDisp(top_node, 2)
            top_disp_z = ops.nodeDisp(top_node, 3)

            # 获取底部节点位移
            bottom_disp_x = ops.nodeDisp(bottom_node, 1)
            bottom_disp_y = ops.nodeDisp(bottom_node, 2)
            bottom_disp_z = ops.nodeDisp(bottom_node, 3)

            # 计算相对位移
            rel_disp_x = top_disp_x - bottom_disp_x
            rel_disp_y = top_disp_y - bottom_disp_y
            rel_disp_z = top_disp_z - bottom_disp_z

            # 初始化桥墩数据结构
            if pier_key not in self.response_data['pier_responses']:
                self.response_data['pier_responses'][pier_key] = {
                    'time': [],
                    'disp_x': [],
                    'disp_y': [],
                    'disp_z': []
                }

            # 记录相对位移数据
            pier_data = self.response_data['pier_responses'][pier_key]
            pier_data['time'].append(current_time)
            pier_data['disp_x'].append(rel_disp_x)
            pier_data['disp_y'].append(rel_disp_y)
            pier_data['disp_z'].append(rel_disp_z)
            # pier_data['disp_x'].append(top_disp_x)
            # pier_data['disp_y'].append(top_disp_y)
            # pier_data['disp_z'].append(top_disp_z)
            
    def _record_bearing_relative_displacements_to_memory(self, current_time):
        """记录支座相对位移到内存（与原始分析器保持一致）"""
        if not self.model.bearings['elements']:
            return

        # 获取代表性支座
        if not hasattr(self, '_selected_bearing_indices'):
            self._selected_bearing_indices = find_bearings_closest_to_y0(self.model)

        # 初始化时间步数据
        if current_time not in self.bearing_relative_disps:
            self.bearing_relative_disps[current_time] = []

        for bearing_idx in self._selected_bearing_indices:
            if bearing_idx >= len(self.model.bearings['connections']):
                continue

            deck_node, support_node = self.model.bearings['connections'][bearing_idx]

            try:
                # 获取节点坐标
                x_coord = ops.nodeCoord(deck_node, 1)
                y_coord = ops.nodeCoord(deck_node, 2)

                # 获取节点位移
                deck_disp_x = ops.nodeDisp(deck_node, 1)
                deck_disp_y = ops.nodeDisp(deck_node, 2)
                deck_disp_z = ops.nodeDisp(deck_node, 3)

                support_disp_x = ops.nodeDisp(support_node, 1)
                support_disp_y = ops.nodeDisp(support_node, 2)
                support_disp_z = ops.nodeDisp(support_node, 3)

                # 计算相对位移
                rel_disp_x = deck_disp_x - support_disp_x
                rel_disp_y = deck_disp_y - support_disp_y
                rel_disp_z = deck_disp_z - support_disp_z

                # 获取支座所属的跨号和元素标签
                span_num = self.model.bearings['spans'][bearing_idx]
                elem_tag = self.model.bearings['elements'][bearing_idx]

                # 记录支座相对位移数据（与原始分析器格式一致）
                bearing_data = {
                    'bearing_idx': bearing_idx,
                    'span': span_num,
                    'x_coord': x_coord,
                    'y_coord': y_coord,
                    'rel_disp_x': rel_disp_x,
                    'rel_disp_y': rel_disp_y,
                    'rel_disp_z': rel_disp_z,
                    'deck_node': deck_node,
                    'support_node': support_node,
                    'elem_tag': elem_tag
                }

                self.bearing_relative_disps[current_time].append(bearing_data)

            except Exception as e:
                print(f"记录支座 {bearing_idx} 相对位移时出错: {e}")
                continue

    def _record_bearing_forces_to_memory(self, current_time):
        """记录支座力和变形数据到内存（用于与文件模式兼容）"""
        if not self.model.bearings['elements']:
            return

        # 获取代表性支座
        if not hasattr(self, '_selected_bearing_indices'):
            self._selected_bearing_indices = find_bearings_closest_to_y0(self.model)

        for bearing_idx in self._selected_bearing_indices:
            if bearing_idx >= len(self.model.bearings['elements']):
                continue

            bearing_elem = self.model.bearings['elements'][bearing_idx]
            deck_node, _ = self.model.bearings['connections'][bearing_idx]

            try:
                # 获取支座力 [Fx, Fy, Fz, Mx, My, Mz]
                forces = ops.eleForce(bearing_elem)
                # 获取支座变形 [dx, dy, dz, rx, ry, rz]
                deformations = ops.eleResponse(bearing_elem, 'deformation')

                # 获取支座坐标
                x_coord = ops.nodeCoord(deck_node, 1)
                y_coord = ops.nodeCoord(deck_node, 2)

                # 初始化支座数据结构
                if bearing_idx not in self.response_data['bearing_responses']:
                    self.response_data['bearing_responses'][bearing_idx] = {
                        'x_coord': x_coord,
                        'y_coord': y_coord,
                        'time': [],
                        'force_x': [],
                        'force_y': [],
                        'force_z': [],
                        'deform_x': [],
                        'deform_y': [],
                        'deform_z': []
                    }

                # 记录数据
                bearing_data = self.response_data['bearing_responses'][bearing_idx]
                bearing_data['time'].append(current_time)
                bearing_data['force_x'].append(forces[0] if len(forces) > 0 else 0.0)
                bearing_data['force_y'].append(forces[1] if len(forces) > 1 else 0.0)
                bearing_data['force_z'].append(forces[2] if len(forces) > 2 else 0.0)
                bearing_data['deform_x'].append(deformations[0] if len(deformations) > 0 else 0.0)
                bearing_data['deform_y'].append(deformations[1] if len(deformations) > 1 else 0.0)
                bearing_data['deform_z'].append(deformations[2] if len(deformations) > 2 else 0.0)

            except Exception as e:
                print(f"记录支座 {bearing_idx} 力和变形时出错: {e}")
                continue
                
    def get_memory_responses(self) -> Tuple[Dict, Dict]:
        """获取内存中的响应数据，转换为与文件读取兼容的格式"""
        pier_data = {}
        bearing_data = {}

        # 处理桥墩数据
        for pier_key, data in self.response_data['pier_responses'].items():
            pier_data[pier_key] = {
                'time': np.array(data['time']),
                'disp_x': np.array(data['disp_x']),
                'disp_y': np.array(data['disp_y']),
                'disp_z': np.array(data['disp_z'])
            }

        # 处理支座数据 - 从内存中的支座响应数据提取
        for bearing_idx, data in self.response_data['bearing_responses'].items():
            if data['time']:  # 确保有数据
                bearing_data[bearing_idx] = {
                    'x_coord': data['x_coord'],
                    'y_coord': data['y_coord'],
                    'force_data': {
                        'time': np.array(data['time']),
                        'force_x': np.array(data['force_x']),
                        'force_y': np.array(data['force_y']),
                        'force_z': np.array(data['force_z'])
                    },
                    'deform_data': {
                        'time': np.array(data['time']),
                        'deform_x': np.array(data['deform_x']),
                        'deform_y': np.array(data['deform_y']),
                        'deform_z': np.array(data['deform_z'])
                    }
                }

        return pier_data, bearing_data
        
    def clear_memory_responses(self):
        """清空内存中的响应数据"""
        self.response_data = {
            'pier_responses': {},
            'bearing_responses': {},
            'time_history': []
        }
        # 也清空支座相对位移数据
        self.bearing_relative_disps = {}
        
    def dynamic(self, h='gm/elCentro.at2', pga=0.15, dt=0.01):
        """重写动力分析方法，支持内存传递模式和跳过不必要操作"""
        # 清空之前的内存数据
        self.clear_memory_responses()

        # 检查是否为纯内存模式（不保存临时文件）
        skip_file_operations = (hasattr(self.response_collector, 'config') and
                                not self.response_collector.config.save_temp_files)

        if skip_file_operations:
            # 纯内存模式：跳过文件操作、绘图和事故检查
            analysis_stats = self._dynamic_memory_only(h, pga, dt)
        else:
            # 标准模式：调用父类方法
            analysis_stats = super().dynamic(h, pga, dt)

        # 如果使用内存传递，将数据传递给收集器
        if self.response_collector:
            pier_data, bearing_data = self.get_memory_responses()

            # 传递桥墩数据
            for pier_key, data in pier_data.items():
                self.response_collector.collect_pier_response(
                    pier_key, data['time'], {
                        'disp_x': data['disp_x'],
                        'disp_y': data['disp_y'],
                        'disp_z': data['disp_z']
                    }
                )

            # 传递支座数据
            for bearing_idx, data in bearing_data.items():
                self.response_collector.collect_bearing_response(
                    bearing_idx,
                    data['force_data']['time'],
                    data['force_data'],
                    data['deform_data'],
                    {'x_coord': data['x_coord'], 'y_coord': data['y_coord']}
                )

        return analysis_stats

    def _dynamic_memory_only(self, h='gm/elCentro.at2', pga=0.15, dt=0.01):
        """纯内存模式的动力分析：支持动态时间步，跳过文件操作、绘图和事故检查"""
        import openseespy.opensees as ops

        # 静力分析
        if not hasattr(self.model, 'bearing_axial_loads'):
            self.static(update_bearing=True)
        ops.reset()

        # 地震动力分析设置
        ops.loadConst('-time', 0.0)
        self._setup_dynamic_analysis()
        self._apply_ground_motion(h, pga, dt)

        # 不设置文件记录器，只进行内存记录
        current_time = 0.0
        target_time = self.dt * self.npts
        dt_output = 0.02  # 输出时间间隔
        self.dt = 0.01    # 分析步长小于荷载采样步长
        dt_analysis = self.dt  # 分析时间步（动态调整）
        dt_min = 1e-5  # 最小时间步

        next_output_time = dt_output  # 下一个输出时间点

        # 记录分析状态
        self.analysis_stats = {
            'total_steps': 0,
            'successful_steps': 0,
            'successful_consecutive': 0,
            'algorithm_switches': 0,
            'skipped_steps': 0,
            'adaptive_steps': 0,  # 时间步调整次数
            'min_dt_used': dt_analysis  # 使用过的最小时间步
        }

        step = 0
        while current_time < target_time - 1e-10:
            step += 1
            backup = False

            # 调整时间步以精确到达下一个输出时间点
            remaining_to_output = next_output_time - current_time
            if remaining_to_output > 0 and dt_analysis > remaining_to_output:
                backup = True
                dt_analysis_backup = dt_analysis
                dt_analysis = remaining_to_output

            success = False

            # 尝试分析当前步
            # try:
            if ops.analyze(1, dt_analysis) == 0:
                success = True
            # except Exception:
            #     pass

            # 如果失败，尝试备选算法
            if not success:
                success = self._try_different_algorithms(dt_analysis)
                if success:
                    self.analysis_stats['algorithm_switches'] += 1

            # 如果仍然失败，减小时间步
            while not success and dt_analysis > dt_min:
                dt_analysis *= 0.5
                self.analysis_stats['adaptive_steps'] += 1
                print(f"\n时间步 {step} (t={current_time:.3f}s) 未收敛，减小时间步为 {dt_analysis:.6f}s...")

                self.analysis_stats['successful_consecutive'] = 0
                if dt_analysis < self.analysis_stats['min_dt_used']:
                    self.analysis_stats['min_dt_used'] = dt_analysis

                # try:
                if ops.analyze(1, dt_analysis) == 0:
                    success = True
                    break
                # except Exception:
                #     pass

                # 尝试备选算法
                if not success:
                    success = self._try_different_algorithms(dt_analysis)
                    if success:
                        self.analysis_stats['algorithm_switches'] += 1
                        break

            # 失败，抛出异常
            if not success:
                raise RuntimeError("分析未收敛")

            # 成功分析当前步
            if success:
                ops.reactions()
                current_time += dt_analysis
                self.analysis_stats['successful_steps'] += 1
                self.analysis_stats['successful_consecutive'] += 1

                # 精确检查是否到达输出时间点
                if abs(current_time - next_output_time) < 1e-9:
                    self._record_responses(current_time)
                    next_output_time += dt_output

                if backup:
                    dt_analysis = dt_analysis_backup
                    
                # 时间步恢复策略：如果连续成功且不在输出时间点附近，尝试增大时间步
                if dt_analysis < self.dt and self.analysis_stats['successful_consecutive']==5:
                    dt_analysis = min(np.round(dt_analysis * 2.0, 5), self.dt)
                    self.analysis_stats['successful_consecutive'] = 0

            self.analysis_stats['total_steps'] += 1

            # 进度显示
            if (self.analysis_stats['total_steps'] % 10 == 0) or (abs(current_time-target_time) < 1e-8):
                progress = current_time / target_time * 100
                print(f"\r分析进度: {progress:.1f}% ({current_time:.3f}s/{target_time:.3f}s, dt={dt_analysis:.6f}s)", end="")

        print(f"\n分析完成: {self.analysis_stats['successful_steps']}/{self.analysis_stats['total_steps']} 步成功")
        if self.analysis_stats['adaptive_steps'] > 0:
            print(f"动态时间步调整次数: {self.analysis_stats['adaptive_steps']}")
            print(f"使用的最小时间步: {self.analysis_stats['min_dt_used']:.6f}s")

        return self.analysis_stats
