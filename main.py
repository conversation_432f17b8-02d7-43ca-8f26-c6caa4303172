"""
简支梁桥地震响应分析主程序

功能:
- 创建参数化简支梁桥有限元模型
- 执行模态分析和非线性动力时程分析
- 评估地震事故风险
"""

import openseespy.opensees as ops
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel
from analysis.analyzer import BridgeAnalyzer


def main():
    """主分析流程"""
    # 1. 创建桥梁模型
    print("=" * 60)
    print("简支梁桥地震响应分析")
    print("=" * 60)
    
    # 从配置文件加载参数
    config_file = "configs/bridge-33-宜山路蒲汇塘桥.json"
    params = BridgeParams(config_file)
    
    # 创建有限元模型
    print("\n正在创建桥梁有限元模型...")
    model = SimplySupportedBeamModel(params)
    print(f"模型创建完成: {params.num_spans}跨简支梁桥")
    
    # 2. 执行分析
    analyzer = BridgeAnalyzer(model)
    
    # 2.1 模态分析
    print("\n正在执行模态分析...")
    num_modes = 3
    analyzer.modal(num_modes)
    
    # 2.2 非线性动力时程分析
    print("\n正在执行地震动力分析...")
    pga = 0.27  # 峰值地面加速度 (g)
    analysis_stats = analyzer.dynamic(pga=pga)
    
    # 3. 输出分析结果摘要
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print(f"地震动峰值加速度: {pga:.2f}g")
    print(f"分析成功率: {analysis_stats['successful_steps']}/{analysis_stats['total_steps']} "
          f"({analysis_stats['successful_steps']/analysis_stats['total_steps']*100:.1f}%)")


if __name__ == "__main__":
    main()

