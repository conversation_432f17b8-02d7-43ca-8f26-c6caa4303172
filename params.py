import numpy as np
from utils.update_params import update_from_config
from utils.calculate_volume import calculate_girder_volume
from utils.calculate_weight import calculate_weight
from utils.calculate_pier_params import calculate_pier_params
from utils.calculate_pier_top_displacement_limit import pier_disp_limit
import openseespy.opensees as ops


class BridgeParams:
    """桥梁参数类
    
    定义简支梁桥的几何、材料、截面和分析参数
    支持从配置文件加载参数并进行规范性验证
    """
    
    def __init__(self, config_file=None):
        # 初始化默认参数
        self._init_geometry_params()
        self._init_material_params()
        self._init_section_params()
        self._init_analysis_params()
        self._init_bearing_params()
        
        # 从配置文件更新参数
        update_from_config(config_file, self)
        
        # 计算派生参数
        self._calculate_derived_params()
        
        # 验证参数合理性
        self.validate_params()

    def _init_geometry_params(self):
        """初始化几何参数"""
        self.num_spans = 3                      # 跨数
        self.span_lengths = [10, 22, 10]        # 各跨跨度 (m)
        self.pier_heights = [2+2.5+0.5]*2       # 桥墩高度 (m): 水面上+水深+埋深
        self.pier_segments = 4                  # 桥墩单元数
        self.deck_width = 13                    # 桥面宽度 (m)
        self.continuous_deck = True             # 是否为连续桥面
        self.block_transverse = True            # 是否有横桥向挡块
        
        # 横桥向布置参数
        self.num_piers_transverse = 3           # 横桥向桥墩数量
        self.pier_spacing_transverse = 6        # 横桥向桥墩间距 (m)
        self.bearing_spacing = 0.5              # 支座间距 (m)

    def _init_material_params(self):
        """初始化材料参数"""
        # 截面类型
        self.girder = "hollow_slab"             # 梁截面类型
        self.rubber = "GBZJ200x200x35"          # 支座类型
        
        # 混凝土材料参数
        self.concrete_materials = {
            "cover": {                          # 保护层混凝土（非约束）
                "grade": "C40",                 # 混凝土标号
                "Ec": None,                     # 弹性模量 (MPa)
                "fc": None,                     # 圆柱体抗压强度 (MPa) - 0.79*40 (40=立方体抗压强度标准值)
                "ec": 0.002,                    # 峰值应变
                "ecu": 0.005,                   # 极限应变
                "ft": None,                     # 抗拉强度 (MPa)
                "Et": None,                     # 受拉软化模量
                "gamma": 0.125,                 # 张拉刚度系数 (服役开裂时降低)
            },
            "core": {                           # 核心区混凝土（约束）
                "grade": "C40",
                "Ec": None,                     # 弹性模量 (MPa)
                "fc": None,                     # 圆柱体抗压强度 (MPa)
                "ec": 0.002,                    # 峰值应变
                "ecu": None,                    # 极限应变 (参考: 0.008)
                "ft": None,                     # 抗拉强度 (MPa)
                "Et": None,                     # 受拉软化模量
                "gamma": 0.125,                 # 张拉刚度系数 (服役开裂时降低)
                "k": 1.4                        # 强度提高系数（约束效应）
            }
        }

        # 钢筋材料参数
        self.steel_materials = {
            "longitudinal": {                   # 纵向钢筋
                "grade": "HRB400",
                "fy": 360,                      # 屈服强度 (MPa)
                "fu": 540,                      # 极限强度 (MPa)
                "eu": 0.09,                     # 极限应变
                "Es": 200000.0                  # 弹性模量 (MPa)
            },
            "transverse": {                     # 横向箍筋
                "grade": "HPB300",
                "fy": 270,                      # 屈服强度设计值 (MPa)
                "fyk": 300,                     # 屈服强度标准值 (MPa)
                "fu": 400,                      # 极限强度 (MPa)
                "eu": 0.09,                     # 极限应变
                "Es": 200000.0                  # 弹性模量 (MPa)
            }
        }

    def _init_section_params(self):
        """初始化截面参数"""
        # 盖梁截面参数
        self.cap_beam_section = {
            "width": 1.4,                       # 盖梁宽度 (m)
            "height": 1.38,                     # 盖梁高度 (m)
            "rebar_dia": 0.020,                 # 钢筋直径 (m)
            "spacing": 0.2,                     # 钢筋间距 (m)
            "concrete_cover": 0.05,             # 保护层厚度 (m)
        }
        
        # 桥墩截面参数
        self.pier_section = {
            "type": "circ",
            "diameter": 1.6,                    # 桥墩直径 (m)
            "concrete_cover": 0.04,             # 混凝土保护层厚度 (m)
            "longitudinal_bars": {
                "diameter": 0.025,              # 纵向钢筋直径 (m)
                "number": 48,                   # 纵向钢筋数量 (配筋率0.6%-4%)
                "material": "longitudinal",     # 材料类型
                "rho": None                     # 配筋率 (计算得出)
            },
            "transverse_bars": {
                "diameter": 0.025,              # 箍筋直径 (m) (>=0.01)
                "spacing": 0.10,                # 箍筋间距 (m) (<=0.1)
                "material": "transverse",       # 材料类型
                "configuration": "hoop",        # 箍筋形式: spiral/hoop
                "rho": None                     # 体积配箍率 (计算得出) (参考: 0.005)
            },
            "concrete": {
                "core": "core",                 # 核心区混凝土材料
                "cover": "cover"                # 保护层混凝土材料
            }
        }

        # 主梁截面参数
        self.girder_section = {
            "hollow_slab": {
                "type": "hollow_slab",
                "height": 0.9,                  # 空心板梁高度 (m)
                "slab_width": 1.0,              # 单个空心板宽度 (m)
                "hollow_width": 0.44,           # 空洞宽度 (m)
                "hollow_height": 0.45,          # 空洞高度 (m)
                "cover": 0.05,                  # 保护层厚度 (m)
                "n_long_rebar": 12,             # 每板纵向钢筋数
                "rebar_dia": 0.016              # 钢筋直径 (m)
            },
            "box": {
                "type": "box",
                "width": 6.0,                   # 梁截面宽度 (m)
                "height": 2.5,                  # 梁截面高度 (m)
                "top_slab_thickness": 0.35,     # 顶板厚度 (m)
                "bottom_slab_thickness": 0.25,  # 底板厚度 (m)
                "web_thickness": 0.4,           # 腹板厚度 (m)
                "n_cells": 1,                   # 箱室数量
                "cover": 0.05,                  # 保护层厚度 (m)
                "n_long_rebar": 12,             # 每腹板纵向钢筋数
                "rebar_dia": 0.025              # 钢筋直径 (m)
            },
        }

    def _init_analysis_params(self):
        """初始化分析参数"""
        # 动力分析参数
        self.damping_ratio = 0.05               # 阻尼比
        
        # 荷载参数
        self.gravity = 9.81                     # 重力加速度 (m/s²)
        self.concrete_density = 2500            # 混凝土密度 (kg/m³)
        self.steel_density = 7850               # 钢筋密度 (kg/m³)

        # 桩土相互作用参数 (JTG 3363-2019 P91)
        self.pile_soil = {
            "enable_ssi": True,                 # 是否考虑土-结构相互作用
            "m_value": 5e6,                     # 水平向抗力系数比例系数 (N/m⁴)
            "group_effect_factor": 0.8,         # 桩基群桩效应系数
            "pile_diameter_pier": 0.6,          # 桥墩桩直径 (m)
            "pile_number_pier": 6,              # 桥墩桩数量
        }
        
        # 桥台参数
        self.abutment = {
            "backwall_height": 2.0,             # 背墙高度 (m)
            "gap": None,                        # 伸缩缝宽度 (m, 计算得出)
            "pile_diameter": 0.6,               # 桩直径 (m)
            "pile_number": 7,                   # 桩数量
        }

    def _init_bearing_params(self):
        """初始化支座参数"""
        self.bearing = {
            "friction": True,                   # 是否考虑支座摩擦滑动
            "friction_coef": 0.25,              # 支座摩擦系数（混凝土接触面）

            "GBZJ200x200x35": {
                "type": "GBZJ200x200x35",
                "G": 1000e3,                    # 动剪切模量 (N/m²)
                "A": 0.04,                      # 剪切面积 (m²)
                "t": 0.035,                     # 总厚度 (m)
                "te": 0.025,                    # 橡胶层总厚度 (m)
                "t0": 0.002,                    # 单层钢板厚度 (m)
                "t1": 0.005,                    # 单层橡胶层厚度 (m)
                "s": 9.5,                       # 形状系数
                "Rck": 361e3,                   # 最大承压力 (N)
            },
            "GBZJ300x350x52": {
                "type": "GBZJ300x350x52",
                "G": 1000e3,                    # 动剪切模量 (N/m²)
                "A": 0.105,                     # 剪切面积 (m²)
                "t": 0.052,                     # 总厚度 (m)
                "te": 0.037,                    # 橡胶层总厚度 (m)
                "t0": 0.003,                    # 单层钢板厚度 (m)
                "t1": 0.008,                    # 单层橡胶层厚度 (m)
                "s": 9.78,                      # 形状系数
                "Rck": 986e3,                   # 最大承压力 (N)
            }
        }
        
        # 重量参数（计算得出）
        self.super_weight = None                # 上部结构重量
        self.sub_weight = None                  # 下部结构重量

    def _calculate_derived_params(self):
        """计算派生参数"""
        self.update_pier_heights()              # 更新桥墩计算高度
        self.calculate_gap()                    # 计算伸缩缝宽度
        self.girder_volume = calculate_girder_volume(self)  # 计算主梁体积
        calculate_weight(self)                  # 计算结构重量
        calculate_pier_params(self)             # 计算桥墩参数

        # 计算事故限值 (CJJ 166-2011)
        self.accidents = {
            "girder_falling": girder_falling_limit(self, degree=6),     # 落梁限值
            "pier_top_displacement": pier_disp_limit(self),             # 桥墩位移限值
            "bearing_failure": bearing_failure_limit(self)              # 支座破坏限值
        }


    def validate_params(self):
        """验证桥梁参数的合理性和规范符合性
        
        检查几何参数、材料参数和配筋参数是否满足规范要求
        """
        # 基本几何参数检查
        assert len(self.span_lengths) == self.num_spans, "跨长数量与跨数不匹配"
        assert self.bearing_spacing > 0, "支座间距必须大于0"
        
        # 多跨桥梁参数检查
        if self.num_spans > 1:
            assert self.num_piers_transverse >= 1, "横桥向桥墩数量必须大于等于1"
            if self.num_piers_transverse > 1:
                assert self.pier_spacing_transverse > 0, "横桥向桥墩间距必须大于0"
            
            # 轴压比检查（CJJ 166-2011）
            assert self.pier_section['etak'] < 0.3, \
                f"轴压比 {self.pier_section['etak']:.4f} 超出规范要求(<0.3)"

            # 箍筋间距检查
            transverse_spacing = self.pier_section['transverse_bars']["spacing"]
            steel_l_params = self.steel_materials["longitudinal"]
            ts_limit = (3 + 6*steel_l_params["fu"]/steel_l_params["fy"]) * \
                self.pier_section['longitudinal_bars']["diameter"]
            assert transverse_spacing <= ts_limit, \
                f"箍筋间距 {transverse_spacing:.2f}m 超出规范要求(<{ts_limit:.2f}m)"
            
            # 体积配箍率检查
            rho_s = self.pier_section['transverse_bars']["rho"]
            assert rho_s >= self.pier_section['rhos_min'], \
                f"墩柱体积配箍率 {rho_s:.4f} 低于规范要求(>={self.pier_section['rhos_min']:.4f})"

            # 纵向配筋率检查
            rebar_ratio = self.pier_section['longitudinal_bars']['rho']
            assert 0.006 <= rebar_ratio <= 0.04, \
                f"墩柱纵向钢筋配筋率 {rebar_ratio:.4f} 超出规范范围(0.006~0.04)"

        return


    def update_pier_heights(self):
        """更新桥墩计算模型高度
        
        考虑盖梁高度和主梁高度对桥墩计算长度的影响
        """
        h_base = self.pier_heights
        h_cap = self.cap_beam_section["height"]
        h_girder = self.girder_section[self.girder]["height"] / 2
        self.pier_heights = [h0 + h_cap + h_girder for h0 in h_base]

    def calculate_gap(self):
        """计算伸缩缝宽度
        
        根据桥梁总长度确定伸缩缝宽度，符合规范要求
        """
        # 确定控制长度
        control_length = np.sum(self.span_lengths) if self.continuous_deck else np.max(self.span_lengths)
        
        # 根据长度确定伸缩缝宽度
        if control_length < 20:
            gap = 0.04
        elif control_length < 30:
            gap = 0.06
        else:
            gap = 0.08
            
        self.abutment["gap"] = gap
        print(f"伸缩缝宽度: {gap:.2f} m (控制长度: {control_length:.1f} m)")


def girder_falling_limit(params, degree=6):
    """计算落梁限值
    
    基于支座抗滑稳定性验算, 参考JTJ004-89规范
    
    Args:
        params: 桥梁参数对象
        degree: 地震烈度
        
    Returns:
        list: 各跨的落梁限值 (m)
    """
    # 梁端至盖梁边缘距离 (cm)
    d0 = 50
    d0 = min(d0, params.cap_beam_section["width"] / 2 * 100)
    
    # 计算各跨落梁限值 (m)
    limits = [0.01 * (d0 + span_length * 100) for span_length in params.span_lengths]
    return limits


def bearing_failure_limit(params):
    """计算支座剪切破坏限值
    
    基于支座容许剪切应变计算不同破坏等级的限值
    # state0: 无破坏    (u < limit[0])
    # state1: 轻微破坏  (limit[0] <= u < limit[1])
    # state2: 中等破坏  (limit[1] <= u < limit[2])
    # state3: 严重破坏  (limit[2] <= u < limit[3])
    # state4: 毁坏      (u >= limit[3])
    
    Args:
        params: 桥梁参数对象
        
    Returns:
        list: 支座破坏限值 [轻微, 中等, 严重, 毁坏] (m)
    """
    # 容许剪切应变
    gamma_limits = np.array([0.7, 1.5, 2.5, 4.5])
    
    # 橡胶层总厚度
    rubber_thickness = params.bearing[params.rubber]["te"]
    
    # 计算各破坏等级限值
    limits = (gamma_limits * rubber_thickness).tolist()
    return limits
